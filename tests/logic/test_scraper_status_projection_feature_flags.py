from datetime import datetime

import pytest
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.logic.models.scraper_status import ScraperStatus
from scraper_service.logic.repositories.organization import _OrganizationModel
from scraper_service.logic.repositories.organization_feature_flags import (
    _OrganizationFeatureFlagsModel,
)
from scraper_service.logic.repositories.scraper_status import ScraperStatusRepository
from scraper_service.logic.repositories.user import _UserModel
from scraper_service.logic.repositories.user_feature_flags import _UserFeatureFlagsModel


@pytest.mark.asyncio()
async def test_scraper_status_projection_feature_flags_scenarios(
    async_session: AsyncSession,
    test_user_id: str,
    test_organization_id: str,
):
    """Test scraper_status_projection view with different feature flag scenarios."""

    # Setup repositories
    scraper_status_repo = ScraperStatusRepository(async_session)

    # Test data setup
    test_scenarios = [
        {
            "name": "no_cache",
            "user_id": "user_no_cache",
            "org_id": "org_no_cache",
            "source": "steam_sales",
            "expected_is_partner": False,
            "expected_managed_scrape": False,
        },
        {
            "name": "only_is_partner_true",
            "user_id": "user_partner_only",
            "org_id": "org_partner_only",
            "source": "microsoft_sales",
            "org_feature_flags": ["is_partner"],
            "expected_is_partner": True,
            "expected_managed_scrape": False,
        },
        {
            "name": "only_managed_scrape_true",
            "user_id": "user_managed_only",
            "org_id": "org_managed_only",
            "source": "gog_sales",
            "user_feature_flags": ["managed_scrape"],
            "expected_is_partner": False,
            "expected_managed_scrape": True,
        },
        {
            "name": "both_flags_true",
            "user_id": "user_both_flags",
            "org_id": "org_both_flags",
            "source": "epic_sales",
            "org_feature_flags": ["is_partner"],
            "user_feature_flags": ["managed_scrape"],
            "expected_is_partner": True,
            "expected_managed_scrape": True,
        },
        {
            "name": "other_flags_no_match",
            "user_id": "user_other_flags",
            "org_id": "org_other_flags",
            "source": "origin_sales",
            "org_feature_flags": ["some_other_flag", "another_flag"],
            "user_feature_flags": ["different_flag", "unrelated_feature"],
            "expected_is_partner": False,
            "expected_managed_scrape": False,
        },
    ]

    # Create test data
    for scenario in test_scenarios:
        # Create scraper status
        scraper_status = ScraperStatus(
            organization_id=scenario["org_id"],
            user_id=scenario["user_id"],
            source=scenario["source"],
            state="CONFIGURED",
        )
        await scraper_status_repo.save(scraper_status)

        # Create user cache entry
        user_cache = _UserModel(
            user_id=scenario["user_id"],
            email=f"{scenario["user_id"]}@test.com",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        async_session.add(user_cache)

        # Create organization cache entry
        org_cache = _OrganizationModel(
            organization_id=scenario["org_id"],
            name=f"Test Org {scenario["name"]}",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        async_session.add(org_cache)

        # Create organization feature flags if specified
        if "org_feature_flags" in scenario:
            org_flags = _OrganizationFeatureFlagsModel(
                organization_id=scenario["org_id"],
                feature_flags=scenario["org_feature_flags"],
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            async_session.add(org_flags)

        # Create user feature flags if specified
        if "user_feature_flags" in scenario:
            user_flags = _UserFeatureFlagsModel(
                user_id=scenario["user_id"],
                feature_flags=scenario["user_feature_flags"],
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            async_session.add(user_flags)

    # Commit all test data
    await async_session.commit()

    # Query the view and verify results
    query = text("""
        SELECT
            organization_id,
            user_id,
            source,
            is_partner,
            managed_scrape,
            user_email,
            organization_name
        FROM scraper_status_projection
        ORDER BY organization_id
    """)

    result = await async_session.execute(query)
    rows = result.fetchall()

    # Verify we have all 5 scenarios
    assert len(rows) == 5

    # Create a mapping for easier verification
    results_by_org = {row.organization_id: row for row in rows}

    # Verify each scenario
    for scenario in test_scenarios:
        row = results_by_org[scenario["org_id"]]

        assert row.organization_id == scenario["org_id"]
        assert row.user_id == scenario["user_id"]
        assert row.source == scenario["source"]
        assert row.is_partner == scenario["expected_is_partner"], (
            f"Scenario {scenario["name"]}: expected is_partner={scenario["expected_is_partner"]}, "
            f"got {row.is_partner}"
        )
        assert row.managed_scrape == scenario["expected_managed_scrape"], (
            f"Scenario {scenario["name"]}: expected managed_scrape={scenario["expected_managed_scrape"]}, "
            f"got {row.managed_scrape}"
        )
        assert row.user_email == f"{scenario["user_id"]}@test.com"
        assert row.organization_name == f"Test Org {scenario["name"]}"

    # Additional verification: Test specific scenarios individually
    no_cache_row = results_by_org["org_no_cache"]
    assert not no_cache_row.is_partner
    assert not no_cache_row.managed_scrape

    partner_only_row = results_by_org["org_partner_only"]
    assert partner_only_row.is_partner
    assert not partner_only_row.managed_scrape

    managed_only_row = results_by_org["org_managed_only"]
    assert not managed_only_row.is_partner
    assert managed_only_row.managed_scrape

    both_flags_row = results_by_org["org_both_flags"]
    assert both_flags_row.is_partner
    assert both_flags_row.managed_scrape

    other_flags_row = results_by_org["org_other_flags"]
    assert not other_flags_row.is_partner
    assert not other_flags_row.managed_scrape
