"""Tests for UserFeatureFlagsRepository.bulk_override method."""

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.logic.entities import UserFeatureFlags
from scraper_service.logic.repositories.user_feature_flags import (
    UserFeatureFlagsRepository,
    _UserFeatureFlagsModel,
)


@pytest.mark.asyncio()
async def test_bulk_override_with_entities(async_session: AsyncSession):
    repo = UserFeatureFlagsRepository(async_session)
    initial_entities = [
        _UserFeatureFlagsModel(user_id="old_user1", feature_flags=["old_flag1"]),
        _UserFeatureFlagsModel(user_id="old_user2", feature_flags=["old_flag2"]),
    ]
    async_session.add_all(initial_entities)
    await async_session.flush()

    new_entities = [
        UserFeatureFlags(id="user1", feature_flags=["flag1", "flag2"]),
        UserFeatureFlags(id="user2", feature_flags=["flag3"]),
        UserFeatureFlags(id="user3", feature_flags=["flag1", "flag4", "flag5"]),
    ]

    await repo.bulk_override(new_entities)
    await async_session.flush()

    result = await async_session.execute(select(_UserFeatureFlagsModel))
    all_entities = result.scalars().all()

    assert len(all_entities) == 3

    # Verify the new entities are correctly stored
    user_ids = {entity.user_id for entity in all_entities}
    assert user_ids == {"user1", "user2", "user3"}

    # Verify specific entity data
    for entity in all_entities:
        if entity.user_id == "user1":
            assert entity.feature_flags == ["flag1", "flag2"]
        elif entity.user_id == "user2":
            assert entity.feature_flags == ["flag3"]
        elif entity.user_id == "user3":
            assert entity.feature_flags == ["flag1", "flag4", "flag5"]


@pytest.mark.asyncio()
async def test_bulk_override_with_empty_list_removes_old_entries(
    async_session: AsyncSession,
):
    repo = UserFeatureFlagsRepository(async_session)

    initial_entities = [
        _UserFeatureFlagsModel(user_id="user1", feature_flags=["flag1"]),
        _UserFeatureFlagsModel(user_id="user2", feature_flags=["flag2"]),
    ]
    async_session.add_all(initial_entities)
    await async_session.flush()

    await repo.bulk_override([])
    await async_session.flush()

    result = await async_session.execute(select(_UserFeatureFlagsModel))
    all_entities = result.scalars().all()

    assert len(all_entities) == 0


@pytest.mark.asyncio()
async def test_bulk_override_on_empty_table(async_session: AsyncSession):
    repo = UserFeatureFlagsRepository(async_session)

    new_entities = [
        UserFeatureFlags(id="user1", feature_flags=["flag1"]),
        UserFeatureFlags(id="user2", feature_flags=["flag2", "flag3"]),
    ]

    await repo.bulk_override(new_entities)
    await async_session.flush()

    result = await async_session.execute(select(_UserFeatureFlagsModel))
    all_entities = result.scalars().all()

    assert len(all_entities) == 2

    user_ids = {entity.user_id for entity in all_entities}
    assert user_ids == {"user1", "user2"}
