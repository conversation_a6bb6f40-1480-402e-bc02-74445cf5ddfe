from datetime import datetime

from sqlalchemy import DateTime, Index, String, delete
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import Base
from scraper_service.logic.entities import UserFeatureFlags


class _UserFeatureFlagsModel(Base):
    __tablename__ = "users_feature_flags_cache"

    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    user_id: Mapped[str] = mapped_column(String(100))
    feature_flags: Mapped[list[str]] = mapped_column(JSONB)

    __table_args__ = (
        Index(
            "ix_users_feature_flags_cache_user_id",
            user_id,
            unique=True,
        ),
    )


class UserFeatureFlagsRepository:
    def __init__(self, session: AsyncSession) -> None:
        self._session: AsyncSession = session

    async def bulk_override(self, entities: list[UserFeatureFlags]) -> None:
        delete_stmt = delete(_UserFeatureFlagsModel)
        await self._session.execute(delete_stmt)

        if not entities:
            return

        new_entities = [
            _UserFeatureFlagsModel(
                user_id=entity.id, feature_flags=entity.feature_flags
            )
            for entity in entities
        ]
        self._session.add_all(new_entities)
