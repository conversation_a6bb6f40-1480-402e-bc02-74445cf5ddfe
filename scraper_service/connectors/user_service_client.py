import requests
from pydantic import BaseModel, RootModel

from scraper_service.config import Config
from scraper_service.logic.entities import (
    Organization,
    OrganizationFeatureFlag,
    OrganizationID,
    User,
    UserFeatureFlag,
    UserID,
)

DEFAULT_TIMEOUT = 5


class UserFeatureFlagResponse(BaseModel):
    data: list[UserFeatureFlag]


class UserServiceClient:
    def __init__(self, config: Config) -> None:
        self._config: Config = config
        self._request_headers = {
            "x-api-key": self._config.user_service_key,
            "User-Agent": self._config.user_agent,
        }

    def _get_paged(
        self, url: str, *, limit: int = 1000, offset: int = 0, **kwargs
    ) -> list[dict]:
        params = kwargs.get("params", {})
        params.update({"limit": limit, "offset": offset})
        kwargs["params"] = params
        data = []
        while True:
            response = requests.get(url=url, **kwargs)
            response_data = response.json()
            data.extend(response_data["data"])

            if len(data) >= response_data["count"] or response_data["count"] == 0:
                break

            kwargs["params"]["offset"] += limit

        return data

    def get_organization_by_id(self, org_id: OrganizationID) -> Organization:
        response = requests.get(
            f"{self._config.user_service_url}/organization/{org_id}",
            headers=self._request_headers,
            timeout=DEFAULT_TIMEOUT,
        )
        response.raise_for_status()
        return Organization.model_validate(response.json())

    def get_user_by_id(self, user_id: UserID) -> User:
        response = requests.get(
            f"{self._config.user_service_url}/user/{user_id}",
            headers=self._request_headers,
            timeout=DEFAULT_TIMEOUT,
        )
        response.raise_for_status()
        return User.model_validate(response.json())

    def get_users_feature_flags(self) -> list[UserFeatureFlag]:
        data = self._get_paged(
            f"{self._config.user_service_url}/user-key-value/search",
            params={"key": "feature.*"},
            headers=self._request_headers,
            timeout=DEFAULT_TIMEOUT,
        )

        return RootModel[list[UserFeatureFlag]].model_validate(data).root

    def get_organizations_feature_flags(self) -> list[OrganizationFeatureFlag]:
        data = self._get_paged(
            f"{self._config.user_service_url}/organization-key-value/search",
            params={"key": "feature.*"},
            headers=self._request_headers,
            timeout=DEFAULT_TIMEOUT,
        )

        return RootModel[list[OrganizationFeatureFlag]].model_validate(data).root
