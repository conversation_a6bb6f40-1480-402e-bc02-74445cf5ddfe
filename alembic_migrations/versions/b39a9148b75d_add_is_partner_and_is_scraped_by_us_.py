"""add is_partner and managed_scrape columns to scraper_status_projection view

Revision ID: b39a9148b75d
Revises: d453692b19c8
Create Date: 2025-08-13 13:49:56.049482

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b39a9148b75d"
down_revision: str | None = "d453692b19c8"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # Drop the existing view
    op.execute("DROP VIEW IF EXISTS scraper_status_projection")

    # Recreate the view with the new columns
    op.execute("""
        CREATE VIEW scraper_status_projection AS
        SELECT
            scraper_status.*,
            users_cache.email AS user_email,
            organizations_cache.name AS organization_name,
            CASE
                WHEN organizations_feature_flags_cache.feature_flags IS NOT NULL
                     AND organizations_feature_flags_cache.feature_flags ? 'is_partner'
                THEN true
                ELSE false
            END AS is_partner,
            CASE
                WHEN users_feature_flags_cache.feature_flags IS NOT NULL
                     AND users_feature_flags_cache.feature_flags ? 'managed_scrape'
                THEN true
                ELSE false
            END AS managed_scrape
        FROM scraper_status
        LEFT JOIN users_cache ON scraper_status.user_id = users_cache.user_id
        LEFT JOIN organizations_cache ON scraper_status.organization_id = organizations_cache.organization_id
        LEFT JOIN organizations_feature_flags_cache ON scraper_status.organization_id = organizations_feature_flags_cache.organization_id
        LEFT JOIN users_feature_flags_cache ON scraper_status.user_id = users_feature_flags_cache.user_id
    """)


def downgrade() -> None:
    # Drop the view with new columns
    op.execute("DROP VIEW IF EXISTS scraper_status_projection")

    # Recreate the original view without the new columns
    op.execute("""
        CREATE VIEW scraper_status_projection AS
        SELECT
            scraper_status.*,
            users_cache.email AS user_email,
            organizations_cache.name AS organization_name
        FROM
            scraper_status
        LEFT JOIN
            users_cache
        ON
            scraper_status.user_id = users_cache.user_id
        LEFT JOIN
            organizations_cache
        ON
            scraper_status.organization_id = organizations_cache.organization_id
    """)
